{"version": "0.2.0", "configurations": [{"name": "Debug MCP Server", "type": "node", "request": "launch", "program": "${workspaceFolder}/src/index.ts", "outFiles": ["${workspaceFolder}/dist/**/*.js"], "runtimeArgs": ["--loader", "tsx/esm"], "env": {"NODE_ENV": "development"}, "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**"]}, {"name": "Debug MCP Server (Built)", "type": "node", "request": "launch", "program": "${workspaceFolder}/dist/index.js", "env": {"NODE_ENV": "development"}, "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**"], "preLaunchTask": "npm: build"}, {"name": "Attach to Docker Container", "type": "node", "request": "attach", "port": 9229, "address": "localhost", "localRoot": "${workspaceFolder}", "remoteRoot": "/app", "skipFiles": ["<node_internals>/**"]}]}