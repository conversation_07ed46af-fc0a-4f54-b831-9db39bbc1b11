# MCP Rancher Calculators

A containerized Model Context Protocol (MCP) tools application that provides number base conversion utilities. This application runs in Docker and can be integrated with Visual Studio Code through the Augment Code extension.

## Features

- **Hex to Decimal Conversion**: Convert hexadecimal numbers to decimal
- **Decimal to Hex Conversion**: Convert decimal numbers to hexadecimal (with optional uppercase)
- **Binary to Decimal Conversion**: Convert binary numbers to decimal
- **Decimal to Binary Conversion**: Convert decimal numbers to binary
- **Octal to Decimal Conversion**: Convert octal numbers to decimal
- **Decimal to Octal Conversion**: Convert decimal numbers to octal
- **Arbitrary Base Conversion**: Convert between any bases from 2 to 36

## Prerequisites

- **Rancher Desktop** (already running on your Windows host)
- **Node.js 18+** (for local development)
- **Visual Studio Code** with Augment Code extension
- **Docker** and **Docker Compose** (available through Rancher Desktop)

## Quick Start

### 1. Install Dependencies

```bash
npm install
```

### 2. Build and Run with Docker Compose

```bash
# Build and start the container
docker-compose up -d

# Check container status
docker-compose ps

# View logs
docker-compose logs -f mcp-rancher-calculators
```

### 3. Test the MCP Server

```bash
# Test basic functionality
docker exec -i mcp-rancher-calculators npm start << EOF
{"jsonrpc": "2.0", "id": 1, "method": "tools/list"}
EOF
```

## Detailed Setup Instructions

### Step 1: Project Setup

1. Clone or create the project directory:
   ```bash
   cd c:\dev\projects-mcp\mcp-rancher-calculators
   ```

2. Install Node.js dependencies:
   ```bash
   npm install
   ```

3. Build the TypeScript code:
   ```bash
   npm run build
   ```

### Step 2: Docker Container Setup

1. Build the Docker image:
   ```bash
   docker build -t mcp-rancher-calculators .
   ```

2. Run with Docker Compose (recommended):
   ```bash
   docker-compose up -d
   ```

   Or run directly with Docker:
   ```bash
   docker run -d --name mcp-rancher-calculators \
     --restart unless-stopped \
     mcp-rancher-calculators
   ```

### Step 3: VS Code Integration

1. **Open the project in VS Code**:
   ```bash
   code .
   ```

2. **Configure Augment Code Extension**:
   - The `.vscode/settings.json` file already contains MCP server configuration
   - The extension should automatically detect the containerized MCP server

3. **Alternative Manual Configuration**:
   If automatic detection doesn't work, add this to your VS Code settings:
   ```json
   {
     "mcp.servers": {
       "rancher-calculators": {
         "command": "docker",
         "args": [
           "exec",
           "-i",
           "mcp-rancher-calculators",
           "npm",
           "start"
         ]
       }
     }
   }
   ```

### Step 4: Testing the Setup

1. **Test Container Health**:
   ```bash
   docker exec mcp-rancher-calculators node -e "console.log('MCP Server is healthy')"
   ```

2. **Test MCP Tools**:
   ```bash
   # List available tools
   echo '{"jsonrpc": "2.0", "id": 1, "method": "tools/list"}' | \
     docker exec -i mcp-rancher-calculators npm start

   # Test hex to decimal conversion
   echo '{"jsonrpc": "2.0", "id": 2, "method": "tools/call", "params": {"name": "hex_to_decimal", "arguments": {"hex": "FF"}}}' | \
     docker exec -i mcp-rancher-calculators npm start
   ```

3. **Test in VS Code**:
   - Open the Command Palette (`Ctrl+Shift+P`)
   - Look for Augment Code commands
   - Try using the MCP tools through the extension

## Available Tools

### hex_to_decimal
Convert hexadecimal to decimal.
```json
{
  "name": "hex_to_decimal",
  "arguments": {
    "hex": "FF"
  }
}
```

### decimal_to_hex
Convert decimal to hexadecimal.
```json
{
  "name": "decimal_to_hex",
  "arguments": {
    "decimal": 255,
    "uppercase": true
  }
}
```

### binary_to_decimal
Convert binary to decimal.
```json
{
  "name": "binary_to_decimal",
  "arguments": {
    "binary": "11111111"
  }
}
```

### decimal_to_binary
Convert decimal to binary.
```json
{
  "name": "decimal_to_binary",
  "arguments": {
    "decimal": 255
  }
}
```

### octal_to_decimal
Convert octal to decimal.
```json
{
  "name": "octal_to_decimal",
  "arguments": {
    "octal": "377"
  }
}
```

### decimal_to_octal
Convert decimal to octal.
```json
{
  "name": "decimal_to_octal",
  "arguments": {
    "decimal": 255
  }
}
```

### base_convert
Convert between any bases (2-36).
```json
{
  "name": "base_convert",
  "arguments": {
    "number": "FF",
    "fromBase": 16,
    "toBase": 10
  }
}
```

## Development

### Local Development

1. **Run in development mode**:
   ```bash
   npm run dev
   ```

2. **Debug in VS Code**:
   - Use the "Debug MCP Server" launch configuration
   - Set breakpoints in the TypeScript source

### Building and Testing

```bash
# Clean build
npm run clean
npm run build

# Run tests (if implemented)
npm test

# Build Docker image
docker build -t mcp-rancher-calculators .
```

## Troubleshooting

### Container Issues

1. **Check container status**:
   ```bash
   docker-compose ps
   docker-compose logs mcp-rancher-calculators
   ```

2. **Restart container**:
   ```bash
   docker-compose restart mcp-rancher-calculators
   ```

3. **Rebuild container**:
   ```bash
   docker-compose down
   docker-compose build --no-cache
   docker-compose up -d
   ```

### VS Code Integration Issues

1. **Check Augment Code extension logs**:
   - Open VS Code Developer Tools
   - Check console for MCP-related errors

2. **Verify MCP server configuration**:
   - Check `.vscode/settings.json`
   - Ensure container is running and accessible

3. **Manual test**:
   ```bash
   docker exec -i mcp-rancher-calculators npm start
   ```
   Then type: `{"jsonrpc": "2.0", "id": 1, "method": "tools/list"}`

### Common Issues

- **Port conflicts**: Ensure no other services are using the configured ports
- **Docker permissions**: Make sure Docker is running and accessible
- **Node.js version**: Ensure Node.js 18+ is installed for local development

## Configuration Files

- `package.json`: Node.js project configuration
- `tsconfig.json`: TypeScript compiler configuration
- `Dockerfile`: Container build instructions
- `docker-compose.yml`: Multi-container orchestration
- `.vscode/settings.json`: VS Code and MCP integration settings
- `mcp-config.json`: MCP server configuration

## Security Considerations

- Container runs as non-root user
- Resource limits configured in docker-compose.yml
- No sensitive data exposed in environment variables
- Health checks enabled for container monitoring

## License

MIT License - see LICENSE file for details.
