# Quick Start Script for MCP Rancher Calculators
# This script provides easy commands to manage the MCP server

param(
    [Parameter(Position=0)]
    [ValidateSet("build", "start", "test", "stop", "logs", "shell", "help")]
    [string]$Action = "help"
)

function Show-Help {
    Write-Host "MCP Rancher Calculators - Quick Start Commands" -ForegroundColor Green
    Write-Host ""
    Write-Host "Usage: .\start-mcp-server.ps1 <action>" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Actions:" -ForegroundColor Cyan
    Write-Host "  build  - Build the Docker image" -ForegroundColor White
    Write-Host "  start  - Start the MCP server interactively" -ForegroundColor White
    Write-Host "  test   - Run automated tests" -ForegroundColor White
    Write-Host "  stop   - Stop all running containers" -ForegroundColor White
    Write-Host "  logs   - View container logs" -ForegroundColor White
    Write-Host "  shell  - Open shell in container" -ForegroundColor White
    Write-Host "  help   - Show this help message" -ForegroundColor White
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Cyan
    Write-Host "  .\start-mcp-server.ps1 build" -ForegroundColor Gray
    Write-Host "  .\start-mcp-server.ps1 start" -ForegroundColor Gray
    Write-Host "  .\start-mcp-server.ps1 test" -ForegroundColor Gray
}

function Build-Image {
    Write-Host "🔨 Building MCP Rancher Calculators Docker image..." -ForegroundColor Yellow
    docker build -t mcp-rancher-calculators .
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Image built successfully!" -ForegroundColor Green
    } else {
        Write-Host "❌ Build failed!" -ForegroundColor Red
        exit 1
    }
}

function Start-Server {
    Write-Host "🚀 Starting MCP server interactively..." -ForegroundColor Yellow
    Write-Host "💡 You can now send JSON-RPC requests. Try:" -ForegroundColor Cyan
    Write-Host '   {"jsonrpc": "2.0", "id": 1, "method": "tools/list"}' -ForegroundColor Gray
    Write-Host '   {"jsonrpc": "2.0", "id": 2, "method": "tools/call", "params": {"name": "hex_to_decimal", "arguments": {"hex": "FF"}}}' -ForegroundColor Gray
    Write-Host ""
    Write-Host "Press Ctrl+C to stop the server" -ForegroundColor Yellow
    Write-Host ""
    docker run --rm -i --name mcp-rancher-calculators mcp-rancher-calculators
}

function Test-Server {
    Write-Host "🧪 Running MCP server tests..." -ForegroundColor Yellow
    node scripts/test-mcp.js
}

function Stop-Containers {
    Write-Host "🛑 Stopping all MCP containers..." -ForegroundColor Yellow
    docker stop $(docker ps -q --filter "name=mcp-rancher-calculators") 2>$null
    docker-compose down 2>$null
    Write-Host "✅ Containers stopped" -ForegroundColor Green
}

function Show-Logs {
    Write-Host "📋 Showing container logs..." -ForegroundColor Yellow
    docker-compose logs -f mcp-rancher-calculators
}

function Open-Shell {
    Write-Host "🐚 Opening shell in container..." -ForegroundColor Yellow
    docker run --rm -it --entrypoint /bin/bash mcp-rancher-calculators
}

# Main execution
switch ($Action) {
    "build" { Build-Image }
    "start" { Start-Server }
    "test" { Test-Server }
    "stop" { Stop-Containers }
    "logs" { Show-Logs }
    "shell" { Open-Shell }
    "help" { Show-Help }
    default { Show-Help }
}
