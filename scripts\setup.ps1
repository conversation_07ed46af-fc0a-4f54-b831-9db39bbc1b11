# PowerShell setup script for MCP Rancher Calculators
# This script sets up the development environment and builds the container

param(
    [switch]$SkipInstall,
    [switch]$SkipBuild,
    [switch]$SkipTest
)

Write-Host "🚀 Setting up MCP Rancher Calculators..." -ForegroundColor Green

# Check prerequisites
Write-Host "`n📋 Checking prerequisites..." -ForegroundColor Yellow

# Check Node.js
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js version: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js not found. Please install Node.js 18+" -ForegroundColor Red
    exit 1
}

# Check Docker
try {
    $dockerVersion = docker --version
    Write-Host "✅ Docker version: $dockerVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker not found. Please ensure Rancher Desktop is running" -ForegroundColor Red
    exit 1
}

# Check Docker Compose
try {
    $composeVersion = docker-compose --version
    Write-Host "✅ Docker Compose version: $composeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker Compose not found" -ForegroundColor Red
    exit 1
}

# Install dependencies
if (-not $SkipInstall) {
    Write-Host "`n📦 Installing Node.js dependencies..." -ForegroundColor Yellow
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Failed to install dependencies" -ForegroundColor Red
        exit 1
    }
    Write-Host "✅ Dependencies installed successfully" -ForegroundColor Green
}

# Build TypeScript
if (-not $SkipBuild) {
    Write-Host "`n🔨 Building TypeScript..." -ForegroundColor Yellow
    npm run build
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Failed to build TypeScript" -ForegroundColor Red
        exit 1
    }
    Write-Host "✅ TypeScript built successfully" -ForegroundColor Green
}

# Build and start Docker containers
Write-Host "`n🐳 Building and starting Docker containers..." -ForegroundColor Yellow
docker-compose up -d --build
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to start containers" -ForegroundColor Red
    exit 1
}

# Wait for container to be ready
Write-Host "`n⏳ Waiting for container to be ready..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Check container health
$containerStatus = docker-compose ps --services --filter "status=running"
if ($containerStatus -match "mcp-rancher-calculators") {
    Write-Host "✅ Container is running" -ForegroundColor Green
} else {
    Write-Host "❌ Container failed to start" -ForegroundColor Red
    docker-compose logs mcp-rancher-calculators
    exit 1
}

# Run tests
if (-not $SkipTest) {
    Write-Host "`n🧪 Running tests..." -ForegroundColor Yellow
    node scripts/test-mcp.js
    if ($LASTEXITCODE -ne 0) {
        Write-Host "⚠️ Some tests failed, but setup is complete" -ForegroundColor Yellow
    } else {
        Write-Host "✅ All tests passed" -ForegroundColor Green
    }
}

Write-Host "`n🎉 Setup completed successfully!" -ForegroundColor Green
Write-Host "`n📝 Next steps:" -ForegroundColor Cyan
Write-Host "1. Open VS Code: code ." -ForegroundColor White
Write-Host "2. Configure Augment Code extension to use the MCP server" -ForegroundColor White
Write-Host "3. Test the tools through VS Code or run: node scripts/test-mcp.js" -ForegroundColor White
Write-Host "`n🔧 Useful commands:" -ForegroundColor Cyan
Write-Host "- View logs: docker-compose logs -f mcp-rancher-calculators" -ForegroundColor White
Write-Host "- Restart: docker-compose restart mcp-rancher-calculators" -ForegroundColor White
Write-Host "- Stop: docker-compose down" -ForegroundColor White
Write-Host "- Test: node scripts/test-mcp.js" -ForegroundColor White
