services:
  mcp-rancher-calculators:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: mcp-rancher-calculators
    restart: unless-stopped

    # Environment variables
    environment:
      - NODE_ENV=production
      - MCP_SERVER_NAME=mcp-rancher-calculators
      - MCP_SERVER_VERSION=1.0.0

    # Network configuration
    networks:
      - mcp-network

    # Resource limits
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.1'
          memory: 64M

    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

    # Health check
    healthcheck:
      test: ["CMD", "node", "-e", "console.log('MCP Server is healthy')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  mcp-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
