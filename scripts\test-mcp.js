#!/usr/bin/env node

/**
 * Test script for MCP Rancher Calculators
 * This script tests all available MCP tools
 */

import { spawn } from 'child_process';
import { promisify } from 'util';

const sleep = promisify(setTimeout);

// Test cases for each tool
const testCases = [
  {
    name: 'List Tools',
    request: {
      jsonrpc: '2.0',
      id: 1,
      method: 'tools/list'
    }
  },
  {
    name: 'Hex to Decimal (FF)',
    request: {
      jsonrpc: '2.0',
      id: 2,
      method: 'tools/call',
      params: {
        name: 'hex_to_decimal',
        arguments: { hex: 'FF' }
      }
    }
  },
  {
    name: 'Decimal to Hex (255)',
    request: {
      jsonrpc: '2.0',
      id: 3,
      method: 'tools/call',
      params: {
        name: 'decimal_to_hex',
        arguments: { decimal: 255, uppercase: true }
      }
    }
  },
  {
    name: 'Binary to Decimal (11111111)',
    request: {
      jsonrpc: '2.0',
      id: 4,
      method: 'tools/call',
      params: {
        name: 'binary_to_decimal',
        arguments: { binary: '11111111' }
      }
    }
  },
  {
    name: 'Decimal to Binary (255)',
    request: {
      jsonrpc: '2.0',
      id: 5,
      method: 'tools/call',
      params: {
        name: 'decimal_to_binary',
        arguments: { decimal: 255 }
      }
    }
  },
  {
    name: 'Octal to Decimal (377)',
    request: {
      jsonrpc: '2.0',
      id: 6,
      method: 'tools/call',
      params: {
        name: 'octal_to_decimal',
        arguments: { octal: '377' }
      }
    }
  },
  {
    name: 'Decimal to Octal (255)',
    request: {
      jsonrpc: '2.0',
      id: 7,
      method: 'tools/call',
      params: {
        name: 'decimal_to_octal',
        arguments: { decimal: 255 }
      }
    }
  },
  {
    name: 'Base Convert (FF from base 16 to base 10)',
    request: {
      jsonrpc: '2.0',
      id: 8,
      method: 'tools/call',
      params: {
        name: 'base_convert',
        arguments: { number: 'FF', fromBase: 16, toBase: 10 }
      }
    }
  }
];

async function testMCPServer() {
  console.log('🧪 Testing MCP Rancher Calculators Server...\n');

  // Check if container is running
  try {
    const checkContainer = spawn('docker', ['ps', '--filter', 'name=mcp-rancher-calculators', '--format', '{{.Names}}']);
    let containerOutput = '';

    checkContainer.stdout.on('data', (data) => {
      containerOutput += data.toString();
    });

    await new Promise((resolve, reject) => {
      checkContainer.on('close', (code) => {
        if (code === 0 && containerOutput.includes('mcp-rancher-calculators')) {
          console.log('✅ Container is running');
          resolve();
        } else {
          console.log('❌ Container is not running. Please start it with: docker-compose up -d');
          reject(new Error('Container not running'));
        }
      });
    });
  } catch (error) {
    console.error('Failed to check container status:', error.message);
    return;
  }

  // Run test cases
  for (const testCase of testCases) {
    console.log(`\n🔍 Testing: ${testCase.name}`);
    console.log(`📤 Request: ${JSON.stringify(testCase.request, null, 2)}`);

    try {
      const result = await runMCPCommand(testCase.request);
      console.log(`📥 Response: ${JSON.stringify(result, null, 2)}`);
      console.log('✅ Test passed');
    } catch (error) {
      console.log(`❌ Test failed: ${error.message}`);
    }

    await sleep(500); // Small delay between tests
  }

  console.log('\n🎉 All tests completed!');
}

function runMCPCommand(request) {
  return new Promise((resolve, reject) => {
    const proc = spawn('docker', ['exec', '-i', 'mcp-rancher-calculators', 'npm', 'start']);
    let output = '';
    let errorOutput = '';

    proc.stdout.on('data', (data) => {
      output += data.toString();
    });

    proc.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });

    proc.on('close', (code) => {
      if (code === 0) {
        try {
          // Parse JSON response
          const lines = output.trim().split('\n');
          const jsonLine = lines.find(line => line.startsWith('{'));
          if (jsonLine) {
            const result = JSON.parse(jsonLine);
            resolve(result);
          } else {
            resolve({ output: output.trim() });
          }
        } catch (parseError) {
          resolve({ output: output.trim() });
        }
      } else {
        reject(new Error(`Command failed with code ${code}: ${errorOutput}`));
      }
    });

    proc.on('error', (error) => {
      reject(error);
    });

    // Send the request
    proc.stdin.write(JSON.stringify(request) + '\n');
    proc.stdin.end();

    // Timeout after 10 seconds
    setTimeout(() => {
      proc.kill();
      reject(new Error('Command timed out'));
    }, 10000);
  });
}

// Run the tests
testMCPServer().catch(console.error);
