# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist/
build/
*.tsbuildinfo

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Git
.git/
.gitignore

# Documentation
README.md
AUDITTRAIL.md
*.md

# Test files
test/
tests/
**/*.test.ts
**/*.test.js
**/*.spec.ts
**/*.spec.js

# Coverage
coverage/
*.lcov

# Logs
logs/
*.log

# Temporary files
tmp/
temp/

# Docker files
Dockerfile*
docker-compose*.yml
.dockerignore
