{"version": "2.0.0", "tasks": [{"type": "npm", "script": "build", "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$tsc"]}, {"type": "npm", "script": "dev", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "isBackground": true, "problemMatcher": ["$tsc-watch"]}, {"label": "Docker: Build", "type": "shell", "command": "docker", "args": ["build", "-t", "mcp-rancher-calculators", "."], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "Docker: Run", "type": "shell", "command": "docker", "args": ["run", "--rm", "-it", "--name", "mcp-rancher-calculators", "mcp-rancher-calculators"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "dependsOn": "Docker: Build"}, {"label": "Docker Compose: Up", "type": "shell", "command": "docker-compose", "args": ["up", "-d"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "Docker Compose: Down", "type": "shell", "command": "docker-compose", "args": ["down"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "Test MCP Tools", "type": "shell", "command": "node", "args": ["-e", "const { spawn } = require('child_process'); const proc = spawn('docker', ['exec', '-i', 'mcp-rancher-calculators', 'npm', 'start']); proc.stdin.write(JSON.stringify({jsonrpc: '2.0', id: 1, method: 'tools/list'}) + '\\n'); proc.stdout.on('data', data => console.log(data.toString())); setTimeout(() => proc.kill(), 5000);"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}}]}