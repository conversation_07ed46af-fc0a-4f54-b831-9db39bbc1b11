{"name": "mcp-rancher-calculators", "version": "1.0.0", "description": "MCP tools application for number base conversions", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "tsx src/index.ts", "test": "jest", "clean": "rm -rf dist"}, "keywords": ["mcp", "model-context-protocol", "calculator", "conversion", "hex", "binary", "decimal"], "author": "MCP Rancher Calculators", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^0.5.0"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0", "tsx": "^4.0.0", "jest": "^29.0.0", "@types/jest": "^29.0.0"}, "engines": {"node": ">=18.0.0"}}