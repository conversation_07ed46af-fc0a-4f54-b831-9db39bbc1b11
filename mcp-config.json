{"mcpServers": {"rancher-calculators": {"name": "Rancher Calculators", "description": "Number base conversion tools for hex, binary, octal, and arbitrary base conversions", "version": "1.0.0", "command": "docker", "args": ["exec", "-i", "mcp-rancher-calculators", "npm", "start"], "env": {"NODE_ENV": "production", "MCP_SERVER_NAME": "mcp-rancher-calculators"}, "capabilities": {"tools": true, "resources": false, "prompts": false}, "tools": [{"name": "hex_to_decimal", "description": "Convert hexadecimal number to decimal", "category": "conversion"}, {"name": "decimal_to_hex", "description": "Convert decimal number to hexadecimal", "category": "conversion"}, {"name": "binary_to_decimal", "description": "Convert binary number to decimal", "category": "conversion"}, {"name": "decimal_to_binary", "description": "Convert decimal number to binary", "category": "conversion"}, {"name": "octal_to_decimal", "description": "Convert octal number to decimal", "category": "conversion"}, {"name": "decimal_to_octal", "description": "Convert decimal number to octal", "category": "conversion"}, {"name": "base_convert", "description": "Convert number between any bases (2-36)", "category": "conversion"}], "autoStart": true, "timeout": 30000, "retries": 3}}, "docker": {"containerName": "mcp-rancher-calculators", "imageName": "mcp-rancher-calculators:latest", "network": "mcp-network", "healthCheck": {"enabled": true, "interval": "30s", "timeout": "10s", "retries": 3}}, "logging": {"level": "info", "file": "./logs/mcp-server.log", "maxSize": "10MB", "maxFiles": 5}}