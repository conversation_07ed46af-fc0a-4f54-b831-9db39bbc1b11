#!/usr/bin/env node

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ErrorCode,
  ListToolsRequestSchema,
  McpError,
} from '@modelcontextprotocol/sdk/types.js';

// Tool definitions
const TOOLS = [
  {
    name: 'hex_to_decimal',
    description: 'Convert hexadecimal number to decimal',
    inputSchema: {
      type: 'object',
      properties: {
        hex: {
          type: 'string',
          description: 'Hexadecimal number (with or without 0x prefix)',
        },
      },
      required: ['hex'],
    },
  },
  {
    name: 'decimal_to_hex',
    description: 'Convert decimal number to hexadecimal',
    inputSchema: {
      type: 'object',
      properties: {
        decimal: {
          type: 'number',
          description: 'Decimal number to convert',
        },
        uppercase: {
          type: 'boolean',
          description: 'Use uppercase letters (default: false)',
          default: false,
        },
      },
      required: ['decimal'],
    },
  },
  {
    name: 'binary_to_decimal',
    description: 'Convert binary number to decimal',
    inputSchema: {
      type: 'object',
      properties: {
        binary: {
          type: 'string',
          description: 'Binary number (with or without 0b prefix)',
        },
      },
      required: ['binary'],
    },
  },
  {
    name: 'decimal_to_binary',
    description: 'Convert decimal number to binary',
    inputSchema: {
      type: 'object',
      properties: {
        decimal: {
          type: 'number',
          description: 'Decimal number to convert',
        },
      },
      required: ['decimal'],
    },
  },
  {
    name: 'octal_to_decimal',
    description: 'Convert octal number to decimal',
    inputSchema: {
      type: 'object',
      properties: {
        octal: {
          type: 'string',
          description: 'Octal number (with or without 0o prefix)',
        },
      },
      required: ['octal'],
    },
  },
  {
    name: 'decimal_to_octal',
    description: 'Convert decimal number to octal',
    inputSchema: {
      type: 'object',
      properties: {
        decimal: {
          type: 'number',
          description: 'Decimal number to convert',
        },
      },
      required: ['decimal'],
    },
  },
  {
    name: 'base_convert',
    description: 'Convert number between any bases (2-36)',
    inputSchema: {
      type: 'object',
      properties: {
        number: {
          type: 'string',
          description: 'Number to convert',
        },
        fromBase: {
          type: 'number',
          description: 'Source base (2-36)',
          minimum: 2,
          maximum: 36,
        },
        toBase: {
          type: 'number',
          description: 'Target base (2-36)',
          minimum: 2,
          maximum: 36,
        },
      },
      required: ['number', 'fromBase', 'toBase'],
    },
  },
];

// Utility functions
function cleanHexInput(hex: string): string {
  return hex.replace(/^0x/i, '').trim();
}

function cleanBinaryInput(binary: string): string {
  return binary.replace(/^0b/i, '').trim();
}

function cleanOctalInput(octal: string): string {
  return octal.replace(/^0o/i, '').trim();
}

function validateBase(base: number): void {
  if (base < 2 || base > 36) {
    throw new McpError(ErrorCode.InvalidParams, `Base must be between 2 and 36, got ${base}`);
  }
}

function validateNumber(number: string, base: number): void {
  const validChars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ'.slice(0, base);
  const upperNumber = number.toUpperCase();

  for (const char of upperNumber) {
    if (!validChars.includes(char)) {
      throw new McpError(
        ErrorCode.InvalidParams,
        `Invalid character '${char}' for base ${base}`
      );
    }
  }
}

// Tool implementations
async function handleHexToDecimal(hex: string): Promise<string> {
  const cleanHex = cleanHexInput(hex);

  if (!/^[0-9A-Fa-f]+$/.test(cleanHex)) {
    throw new McpError(ErrorCode.InvalidParams, 'Invalid hexadecimal number');
  }

  const decimal = parseInt(cleanHex, 16);
  return `Hexadecimal ${hex} = Decimal ${decimal}`;
}

async function handleDecimalToHex(decimal: number, uppercase: boolean = false): Promise<string> {
  if (!Number.isInteger(decimal) || decimal < 0) {
    throw new McpError(ErrorCode.InvalidParams, 'Decimal must be a non-negative integer');
  }

  const hex = decimal.toString(16);
  const result = uppercase ? hex.toUpperCase() : hex;
  return `Decimal ${decimal} = Hexadecimal 0x${result}`;
}

async function handleBinaryToDecimal(binary: string): Promise<string> {
  const cleanBinary = cleanBinaryInput(binary);

  if (!/^[01]+$/.test(cleanBinary)) {
    throw new McpError(ErrorCode.InvalidParams, 'Invalid binary number');
  }

  const decimal = parseInt(cleanBinary, 2);
  return `Binary ${binary} = Decimal ${decimal}`;
}

async function handleDecimalToBinary(decimal: number): Promise<string> {
  if (!Number.isInteger(decimal) || decimal < 0) {
    throw new McpError(ErrorCode.InvalidParams, 'Decimal must be a non-negative integer');
  }

  const binary = decimal.toString(2);
  return `Decimal ${decimal} = Binary 0b${binary}`;
}

async function handleOctalToDecimal(octal: string): Promise<string> {
  const cleanOctal = cleanOctalInput(octal);

  if (!/^[0-7]+$/.test(cleanOctal)) {
    throw new McpError(ErrorCode.InvalidParams, 'Invalid octal number');
  }

  const decimal = parseInt(cleanOctal, 8);
  return `Octal ${octal} = Decimal ${decimal}`;
}

async function handleDecimalToOctal(decimal: number): Promise<string> {
  if (!Number.isInteger(decimal) || decimal < 0) {
    throw new McpError(ErrorCode.InvalidParams, 'Decimal must be a non-negative integer');
  }

  const octal = decimal.toString(8);
  return `Decimal ${decimal} = Octal 0o${octal}`;
}

async function handleBaseConvert(number: string, fromBase: number, toBase: number): Promise<string> {
  validateBase(fromBase);
  validateBase(toBase);
  validateNumber(number, fromBase);

  // Convert to decimal first, then to target base
  const decimal = parseInt(number, fromBase);
  const result = decimal.toString(toBase);

  return `Base ${fromBase}: ${number} = Base ${toBase}: ${result} (Decimal: ${decimal})`;
}

// Create and configure the server
const server = new Server(
  {
    name: 'mcp-rancher-calculators',
    version: '1.0.0',
  },
  {
    capabilities: {
      tools: {},
    },
  }
);

// Handle tool listing
server.setRequestHandler(ListToolsRequestSchema, async () => {
  return {
    tools: TOOLS,
  };
});

// Handle tool calls
server.setRequestHandler(CallToolRequestSchema, async (request) => {
  const { name, arguments: args } = request.params;

  if (!args) {
    throw new McpError(ErrorCode.InvalidParams, 'Missing arguments');
  }

  try {
    switch (name) {
      case 'hex_to_decimal':
        return {
          content: [
            {
              type: 'text',
              text: await handleHexToDecimal(args.hex as string),
            },
          ],
        };

      case 'decimal_to_hex':
        return {
          content: [
            {
              type: 'text',
              text: await handleDecimalToHex(args.decimal as number, args.uppercase as boolean),
            },
          ],
        };

      case 'binary_to_decimal':
        return {
          content: [
            {
              type: 'text',
              text: await handleBinaryToDecimal(args.binary as string),
            },
          ],
        };

      case 'decimal_to_binary':
        return {
          content: [
            {
              type: 'text',
              text: await handleDecimalToBinary(args.decimal as number),
            },
          ],
        };

      case 'octal_to_decimal':
        return {
          content: [
            {
              type: 'text',
              text: await handleOctalToDecimal(args.octal as string),
            },
          ],
        };

      case 'decimal_to_octal':
        return {
          content: [
            {
              type: 'text',
              text: await handleDecimalToOctal(args.decimal as number),
            },
          ],
        };

      case 'base_convert':
        return {
          content: [
            {
              type: 'text',
              text: await handleBaseConvert(
                args.number as string,
                args.fromBase as number,
                args.toBase as number
              ),
            },
          ],
        };

      default:
        throw new McpError(ErrorCode.MethodNotFound, `Unknown tool: ${name}`);
    }
  } catch (error) {
    if (error instanceof McpError) {
      throw error;
    }
    throw new McpError(ErrorCode.InternalError, `Tool execution failed: ${error}`);
  }
});

// Start the server
async function main() {
  const transport = new StdioServerTransport();
  await server.connect(transport);
  console.error('MCP Rancher Calculators server running on stdio');
}

main().catch((error) => {
  console.error('Server failed to start:', error);
  process.exit(1);
});
