{"name": "MCP Rancher Calculators Configuration for Augment Code", "description": "Configuration file for integrating the containerized MCP tools with Augment Code extension", "version": "1.0.0", "mcp": {"servers": {"rancher-calculators": {"name": "Rancher Calculators", "description": "Number base conversion tools (hex, binary, octal, arbitrary bases)", "command": "docker", "args": ["run", "--rm", "-i", "mcp-rancher-calculators"], "env": {"NODE_ENV": "production"}, "capabilities": {"tools": true}, "tools": [{"name": "hex_to_decimal", "description": "Convert hexadecimal to decimal", "example": "FF → 255"}, {"name": "decimal_to_hex", "description": "Convert decimal to hexadecimal", "example": "255 → 0xFF"}, {"name": "binary_to_decimal", "description": "Convert binary to decimal", "example": "11111111 → 255"}, {"name": "decimal_to_binary", "description": "Convert decimal to binary", "example": "255 → 0b11111111"}, {"name": "octal_to_decimal", "description": "Convert octal to decimal", "example": "377 → 255"}, {"name": "decimal_to_octal", "description": "Convert decimal to octal", "example": "255 → 0o377"}, {"name": "base_convert", "description": "Convert between any bases (2-36)", "example": "FF (base 16) → 11111111 (base 2)"}]}}}, "instructions": {"setup": ["1. <PERSON><PERSON> Dock<PERSON> is running (<PERSON>er Deskt<PERSON>)", "2. Build the image: docker build -t mcp-rancher-calculators .", "3. Configure Augment Code extension with the server settings above", "4. Test with: docker run --rm -i mcp-rancher-calculators"], "usage": ["The MCP server provides 7 number conversion tools", "All tools follow JSON-RPC 2.0 protocol", "Tools can handle various input formats (0x, 0b, 0o prefixes)", "Base conversion supports bases 2-36"]}}