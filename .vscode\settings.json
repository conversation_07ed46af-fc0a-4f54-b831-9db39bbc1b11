{"typescript.preferences.includePackageJsonAutoImports": "auto", "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll": "explicit", "source.organizeImports": "explicit"}, "files.exclude": {"**/node_modules": true, "**/dist": true, "**/.git": true, "**/.DS_Store": true, "**/Thumbs.db": true}, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/coverage": true}, "files.watcherExclude": {"**/node_modules/**": true, "**/dist/**": true}, "mcp.servers": {"rancher-calculators": {"command": "docker", "args": ["exec", "-i", "mcp-rancher-calculators", "npm", "start"], "env": {"NODE_ENV": "production"}}}, "augment.mcpServers": [{"name": "rancher-calculators", "description": "Number base conversion calculators", "command": "docker", "args": ["exec", "-i", "mcp-rancher-calculators", "npm", "start"], "env": {"NODE_ENV": "production"}, "autoStart": true}]}